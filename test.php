<?php

include "Mmb.php";

$mmb = new Mmb("5433318119:AAEqvjCWmC7g1tNvREje1n2qOjuZ-1ThoM0");
$channel = "TaNzAkheRat";
$template = ">> @TanzAkherat <<";


if($upd = $mmb->getUpd()) {
    if($post = $upd->post) {

        if(!eqi($post->chat->username, $channel)) exit;
        if($post->forwarded) exit;

        $text = $post->text;
        
        $text = preg_replace('/(\n|^).*@.*(\n|$)/', "\n", $text);
        $text .= "\n\n" . $template;


        if($post->type == 'text') {
            $post->editText($text);
        }
        elseif($post->media) {
            $post->editCaption($text);
        }

    }
}
elseif(isset($_GET['setwebhook'])) {
    $mmb->setWebhookThis();
}
